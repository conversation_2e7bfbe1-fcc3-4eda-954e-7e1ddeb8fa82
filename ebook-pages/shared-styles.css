/* 电子书A4页面共享样式 */
:root {
    --font-size-body: 14px;
    --font-size-h5: 14px;       
    --font-size-h4: 16px;    
    --font-size-h3: 20px;     
    --font-size-h2: 24px;    
    --font-size-h1: 32px;    
    --color-text: #333333;
    --color-h1: #7BA98C;
    --color-h2: #8DBB9E;
    --color-emphasis-1: #ACDA8C;
    --color-emphasis-2: #C5E1B3;
    --color-bg-light: #F0F7ED;
    --primary-color: #05668d;
    --secondary-color: #427aa1;
    --background-color: #ffffff;
    --accent-color: #679436;
    --highlight-color: #a5be00;
    --text-primary: #2c3e50;
    --text-secondary: #427aa1;
    --text-light: #ebf2fa;
    --border-color: #427aa1;
}

/* A4页面设置 */
@page {
    size: 210mm 297mm;
    margin: 20mm;
    @top-right {
        content: element(page-header);
        font-size: 10px;
        color: #666;
    }
    @bottom-center {
        content: counter(page);
        font-size: 10px;
        color: #666;
    }
}

body {
    font-family: "Source Han Sans CN", "思源黑体 CN", "Microsoft YaHei", sans-serif;
    font-size: var(--font-size-body);
    color: var(--text-primary);
    line-height: 1.6;
    letter-spacing: 0.01em;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    width: 210mm;
    min-height: 297mm;
    box-sizing: border-box;
    counter-reset: page;
}

.page-container {
    width: 210mm;
    min-height: 297mm;
    padding: 20mm;
    box-sizing: border-box;
    background-color: white;
    margin: 0 auto;
    position: relative;
    page-break-after: always;
}

.page-container:last-child {
    page-break-after: auto;
}

/* 页眉 */
.page-header {
    position: fixed;
    top: 10mm;
    right: 20mm;
    font-size: 10px;
    color: #666;
    font-weight: normal;
    z-index: 1000;
}

/* 页脚 */
.page-footer {
    position: fixed;
    bottom: 10mm;
    left: 50%;
    transform: translateX(-50%);
    font-size: 10px;
    color: #666;
    z-index: 1000;
}

/* 内容区域 */
.content-area {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Source Han Sans CN Bold', '思源黑体 CN Bold', "Microsoft YaHei", sans-serif;
    margin-top: 1.5em;
    margin-bottom: 1em;
    line-height: 1.3;
    letter-spacing: -0.01em;
    page-break-after: avoid;
}

h1 {
    font-size: var(--font-size-h1);
    font-weight: 700;
    text-align: center;
    margin-bottom: 1.5em;
    color: var(--color-h1);
    border-bottom: 2px solid var(--color-h1);
    padding-bottom: 0.5em;
}

h2 {
    font-size: var(--font-size-h2);
    font-weight: 600;
    color: var(--color-h1);
    border-bottom: 1px solid #ccc;
    padding-bottom: 0.3em;
    margin-top: 2em;
}

h3 {
    font-size: var(--font-size-h3);
    font-weight: 500;
    color: var(--color-h2);
    margin-top: 1.5em;
}

h4 {
    font-size: var(--font-size-h4);
    font-weight: 500;
    color: var(--color-h2);
}

/* 段落样式 */
p {
    margin-bottom: 1em;
    margin-top: 0.5em;
    text-align: justify;
    line-height: 1.6;
    letter-spacing: 0.01em;
    font-weight: 400;
    page-break-inside: avoid;
    orphans: 2;
    widows: 2;
}

/* 列表样式 */
ul, ol {
    margin-bottom: 1em;
    padding-left: 1.5em;
    line-height: 1.5;
    page-break-inside: avoid;
}

li {
    margin-bottom: 0.3em;
    page-break-inside: avoid;
}

/* 表格样式 */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1.5em;
    font-size: 0.9em;
    page-break-inside: avoid;
}

th, td {
    border: 1px solid #dee2e6;
    padding: 0.6em;
    text-align: left;
    vertical-align: top;
}

th {
    background-color: #f2f2f2;
    font-weight: 500;
    color: var(--color-h2);
}

/* 图表容器 */
.chart-container {
    width: 100%;
    max-width: 160mm;
    height: 300px;
    margin: 1.5em auto;
    padding: 0.8em;
    border: 1px solid #eee;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    page-break-inside: avoid;
}

/* 图片样式 */
.image-suggestion {
    display: block;
    margin: 1.5em auto;
    padding: 1em;
    border: 1px dashed #ccc;
    background-color: var(--color-bg-light);
    text-align: center;
    font-style: italic;
    color: #555;
    page-break-inside: avoid;
}

.image-suggestion img {
    max-width: 100%;
    height: auto;
    margin-top: 0.5em;
    border: 1px solid #ddd;
}

figcaption {
    font-size: 0.8em;
    color: #7F8896;
    text-align: center;
    margin-top: 0.5em;
    font-style: normal;
    font-family: 'Source Han Sans CN Light', '思源黑体 CN Light', sans-serif;
}

/* 链接样式 */
a {
    color: var(--primary-color);
    text-decoration: none;
}

a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

/* 引用块样式 */
blockquote {
    border-left: 4px solid var(--color-emphasis-1);
    padding: 1em 1.5em;
    margin: 1.5em 0;
    background-color: var(--color-bg-light);
    color: var(--color-text);
    font-style: italic;
    border-radius: 0 8px 8px 0;
    page-break-inside: avoid;
}

/* 强调样式 */
.highlight-recommendation {
    background-color: rgba(172, 218, 140, 0.15);
    border-radius: 6px;
    padding: 1em;
    border-left: 4px solid var(--color-emphasis-1);
    page-break-inside: avoid;
}

/* 打印优化 */
@media print {
    body {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    .page-container {
        page-break-after: always;
    }
    
    .page-container:last-child {
        page-break-after: auto;
    }
    
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
    
    p, li, blockquote {
        page-break-inside: avoid;
        orphans: 2;
        widows: 2;
    }
    
    .chart-container, .image-suggestion, table {
        page-break-inside: avoid;
    }
}

/* 封面特殊样式 */
.cover-page {
    text-align: center;
    padding: 4em 2em;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.cover-page h1 {
    font-size: 2.5em;
    margin-bottom: 2em;
    border-bottom: none;
    color: var(--color-h1);
    line-height: 1.2;
}

.cover-page p {
    font-size: 1.1em;
    margin-bottom: 1em;
    text-align: center;
    line-height: 1.5;
}

/* 目录特殊样式 */
.toc-page {
    padding: 2em 0;
}

.toc-page h2 {
    font-size: 1.8em;
    margin-top: 0;
    margin-bottom: 1.5em;
    text-align: center;
    border-bottom: none;
    color: var(--color-h1);
}

.toc-page ul {
    list-style-type: none;
    padding-left: 0;
}

.toc-page ul li {
    margin-bottom: 0.5em;
    line-height: 1.4;
}

.toc-page ul li a {
    text-decoration: none;
    color: var(--color-text);
    font-weight: 400;
}

.toc-page ul li a:hover {
    color: var(--color-emphasis-1);
}

.toc-page ul ul {
    padding-left: 1.5em;
    margin-top: 0.3em;
}

.toc-page > ul > li > a {
    font-weight: 500;
}
