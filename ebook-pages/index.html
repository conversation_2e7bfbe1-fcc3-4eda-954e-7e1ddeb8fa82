<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>宋庄镇农业数字化与乡村旅游融合发展可行性研究报告 - 电子书页面索引</title>
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        .index-container {
            max-width: 800px;
            margin: 2em auto;
            padding: 2em;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .index-title {
            text-align: center;
            color: var(--color-h1);
            margin-bottom: 2em;
            border-bottom: 2px solid var(--color-h1);
            padding-bottom: 0.5em;
        }
        
        .page-list {
            list-style: none;
            padding: 0;
        }
        
        .page-list li {
            margin-bottom: 1em;
            padding: 1em;
            background-color: var(--color-bg-light);
            border-radius: 6px;
            border-left: 4px solid var(--color-emphasis-1);
        }
        
        .page-list li a {
            text-decoration: none;
            color: var(--text-primary);
            font-weight: 500;
            display: block;
            font-size: 1.1em;
        }
        
        .page-list li a:hover {
            color: var(--color-h2);
        }
        
        .page-description {
            font-size: 0.9em;
            color: var(--text-secondary);
            margin-top: 0.5em;
            font-style: italic;
        }
        
        .section-header {
            background-color: var(--color-h2);
            color: white;
            padding: 0.8em 1em;
            margin: 1.5em 0 1em 0;
            border-radius: 4px;
            font-weight: 600;
        }
        
        .usage-note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 1em;
            border-radius: 6px;
            margin-bottom: 2em;
        }
        
        .usage-note h3 {
            color: #856404;
            margin-top: 0;
        }
        
        .usage-note p {
            color: #856404;
            margin-bottom: 0.5em;
        }
    </style>
</head>
<body>
    <div class="index-container">
        <h1 class="index-title">宋庄镇农业数字化与乡村旅游融合发展可行性研究报告</h1>
        <h2 style="text-align: center; color: var(--color-h2); margin-bottom: 2em;">电子书页面索引</h2>
        
        <div class="usage-note">
            <h3>使用说明</h3>
            <p>• 本报告已按照A4规格切割成多个独立的HTML页面，适合制作电子书</p>
            <p>• 每个页面都包含标准的A4尺寸设置（210mm × 297mm）和20mm安全边距</p>
            <p>• 页眉显示章节标题，页脚显示页码</p>
            <p>• 所有页面共享统一的样式文件（shared-styles.css）</p>
            <p>• 图表使用ECharts库，需要网络连接才能正常显示</p>
        </div>
        
        <div class="section-header">前置页面</div>
        <ul class="page-list">
            <li>
                <a href="01-cover.html" target="_blank">01-cover.html - 封面页</a>
                <div class="page-description">包含报告标题、编制单位、日期等基本信息，采用居中布局和渐变背景</div>
            </li>
            <li>
                <a href="02-table-of-contents.html" target="_blank">02-table-of-contents.html - 目录页（第一页）</a>
                <div class="page-description">包含第一部分到第六部分的目录结构</div>
            </li>
            <li>
                <a href="02b-table-of-contents-continued.html" target="_blank">02b-table-of-contents-continued.html - 目录页（续）</a>
                <div class="page-description">包含第七部分到第十四部分的目录结构，以及图目录和表目录</div>
            </li>
        </ul>
        
        <div class="section-header">正文内容</div>
        <ul class="page-list">
            <li>
                <a href="03-executive-summary.html" target="_blank">03-executive-summary.html - 执行摘要</a>
                <div class="page-description">项目核心内容概述，包含效益预估图表</div>
            </li>
            <li>
                <a href="04-part1-project-overview.html" target="_blank">04-part1-project-overview.html - 第一部分：项目概述（第一页）</a>
                <div class="page-description">包含项目背景和项目定义与范围</div>
            </li>
            <li>
                <a href="04b-part1-research-methods.html" target="_blank">04b-part1-research-methods.html - 第一部分：项目概述（续）</a>
                <div class="page-description">包含研究方法与过程的详细说明</div>
            </li>
            <li>
                <a href="05-part2-background-analysis.html" target="_blank">05-part2-background-analysis.html - 第二部分：建设背景与必要性分析（第一页）</a>
                <div class="page-description">包含国家及北京市相关政策环境分析</div>
            </li>
            <li>
                <a href="05b-part2-current-status.html" target="_blank">05b-part2-current-status.html - 第二部分：建设背景与必要性分析（续）</a>
                <div class="page-description">包含北京市及通州区数字农业与乡村旅游发展现状</div>
            </li>
            <li>
                <a href="05c-part2-songzhuang-analysis.html" target="_blank">05c-part2-songzhuang-analysis.html - 第二部分：建设背景与必要性分析（续2）</a>
                <div class="page-description">包含宋庄镇农业与旅游业发展现状与核心痛点分析，含销售渠道结构图表</div>
            </li>
        </ul>
        
        <div class="section-header">待完成页面</div>
        <ul class="page-list">
            <li>
                <div style="color: #666; font-style: italic;">第二部分剩余内容（发展趋势、必要性总结）</div>
            </li>
            <li>
                <div style="color: #666; font-style: italic;">第三部分：宋庄镇资源与条件分析（含SWOT分析图表）</div>
            </li>
            <li>
                <div style="color: #666; font-style: italic;">第四部分：功能定位与目标任务</div>
            </li>
            <li>
                <div style="color: #666; font-style: italic;">第五部分：需求分析</div>
            </li>
            <li>
                <div style="color: #666; font-style: italic;">第六部分：区位条件分析</div>
            </li>
            <li>
                <div style="color: #666; font-style: italic;">第七部分：建设内容与技术方案</div>
            </li>
            <li>
                <div style="color: #666; font-style: italic;">第八部分：组织运行管理</div>
            </li>
            <li>
                <div style="color: #666; font-style: italic;">第九部分：投资估算与资金筹措</div>
            </li>
            <li>
                <div style="color: #666; font-style: italic;">第十部分：效益分析</div>
            </li>
            <li>
                <div style="color: #666; font-style: italic;">第十一部分：风险分析与应对措施</div>
            </li>
            <li>
                <div style="color: #666; font-style: italic;">第十二部分：实施计划与进度安排</div>
            </li>
            <li>
                <div style="color: #666; font-style: italic;">第十三部分：结论与建议</div>
            </li>
            <li>
                <div style="color: #666; font-style: italic;">第十四部分：附录</div>
            </li>
        </ul>
        
        <div class="section-header">技术说明</div>
        <div style="background-color: #f8f9fa; padding: 1em; border-radius: 6px; margin-top: 1em;">
            <h4 style="margin-top: 0; color: var(--color-h2);">样式特点：</h4>
            <ul>
                <li>标准A4尺寸：210mm × 297mm</li>
                <li>安全边距：20mm（上下左右）</li>
                <li>页眉：显示章节标题，位置固定在页面顶部</li>
                <li>页脚：显示页码，位置固定在页面底部中央</li>
                <li>字体：思源黑体 CN / Microsoft YaHei</li>
                <li>正文字号：14px，行距：1.6</li>
                <li>标题层级：H1(32px) > H2(24px) > H3(20px) > H4(16px)</li>
                <li>打印优化：支持分页控制，避免内容截断</li>
            </ul>
            
            <h4 style="color: var(--color-h2);">制作电子书建议：</h4>
            <ul>
                <li>可以直接使用这些HTML文件转换为PDF</li>
                <li>建议使用Chrome浏览器的"打印"功能，选择"保存为PDF"</li>
                <li>打印设置：选择A4纸张，边距设置为"最小值"</li>
                <li>如需要，可以将多个PDF文件合并成一个完整的电子书</li>
            </ul>
        </div>
    </div>
</body>
</html>
